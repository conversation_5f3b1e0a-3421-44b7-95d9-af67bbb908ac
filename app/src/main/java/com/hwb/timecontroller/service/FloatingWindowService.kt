package com.hwb.timecontroller.service

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.app.AlertDialog
import android.app.Service
import android.app.admin.DevicePolicyManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import com.elvishew.xlog.XLog
import com.hwb.timecontroller.R
import com.hwb.timecontroller.activity.PasswordDialogActivity

import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.business.AppLifecycleListener
import com.hwb.timecontroller.business.AppLifecycleManager
import com.hwb.timecontroller.business.CountdownManager
import com.hwb.timecontroller.business.OwnAppVisibilityListener
import com.hwb.timecontroller.business.UserLoginStatusListener
import com.hwb.timecontroller.business.UserManager
import com.hwb.timecontroller.business.WhitelistManager

import com.hwb.timecontroller.service.keepalive.KeepAliveCapable
import com.hwb.timecontroller.service.keepalive.MutualKeepAliveManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit


/**
 * 全局悬浮窗服务
 * 显示倒计时信息和快捷操作按钮
 * 支持互相保活机制
 */
class FloatingWindowService : Service(), KeepAliveCapable, AppLifecycleListener,
    OwnAppVisibilityListener, UserLoginStatusListener {

    companion object {
        // 例外Activity列表：即使是自己应用的Activity，也显示悬浮窗
        private val exceptionActivityClasses = mutableSetOf<String>(
            PasswordDialogActivity::class.java.name,
            "com.hwb.timecontroller.activity.MainActivity",
        )

        /**
         * 启动悬浮窗服务
         */
        fun start(context: Context) {
            val intent = Intent(context, FloatingWindowService::class.java)
            try {
                // Android 8.0+ 需要使用前台服务
                context.startForegroundService(intent)
            } catch (e: Exception) {
                XLog.e("启动FloatingWindowService失败", e)
            }
        }

    }

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null

    // UI组件
    private var tvCountdown: TextView? = null
    private var btnHome: View? = null
    private var btnArrow: ImageButton? = null
    private var layoutMain: LinearLayout? = null
    private var layoutWarning: LinearLayout? = null

    // 保活管理器
    private lateinit var keepAliveManager: MutualKeepAliveManager

    // 协程相关
    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())
    private var countdownObserverJob: Job? = null

    // 拖拽相关
    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f

    // 悬浮窗可见性控制
    private var isFloatingWindowVisible = false

    // 服务销毁标志，防止在销毁过程中执行显示/隐藏操作
    private var isServiceDestroying = false

    // 悬浮窗展开状态
    private var isExpanded = false

    // 充值提醒相关
    private var pulseAnimator: ObjectAnimator? = null
    private var colorAnimator: ValueAnimator? = null
    private var isRechargeNotificationActive = false
    private val rechargeHandler = Handler(Looper.getMainLooper())

    override fun onCreate() {
        super.onCreate()
        XLog.d("悬浮窗服务开始创建")

        // 使用统一的前台服务启动方法，添加异常处理
        try {
            startForegroundServiceSafely(this, "悬浮窗服务")
        } catch (e: Exception) {
            XLog.e("悬浮窗服务前台服务启动失败", e)
            // 悬浮窗服务如果前台服务启动失败，直接停止服务
            stopSelf()
            return
        }

        // 初始化保活管理器
        keepAliveManager = MutualKeepAliveManager(
            context = this,
            currentServiceClass = FloatingWindowService::class.java,
            serviceDisplayName = "悬浮窗服务"
        )

        // 注册需要保活的伙伴服务
        keepAliveManager.registerPartnerServices(
            CountdownService::class.java,
            AppLifecycleManagerService::class.java,
            AppLifecycleAccessibilityService::class.java,
            CheckService::class.java
        )

        // 启动保活
        keepAliveManager.startKeepAlive()

        // 首先检查设备所有者权限
        if (!isDeviceOwner()) {
            XLog.w("没有设备所有者权限，停止悬浮窗服务")
            stopSelf()
            return
        }

        if (checkOverlayPermission()) {
            createFloatingWindow()
            observeCountdownState()

            // 注册应用生命周期监听器
            AppLifecycleManager.addListener(this)

            // 注册用户登录状态监听器
            UserManager.addLoginStatusListener(this)

            // 检查初始状态：基于当前Activity决定悬浮窗状态
            val currentOwnActivity = AppLifecycleManager.getCurrentOwnActivity()
            if (currentOwnActivity != null) {
                // 有自己应用的Activity可见
                updateFloatingWindowVisibility(true, currentOwnActivity)
                XLog.d("初始状态：自己应用Activity($currentOwnActivity)可见，根据规则设置悬浮窗状态")
            } else {
                // 没有自己应用的Activity可见，显示悬浮窗
                showFloatingWindow()
                XLog.d("初始状态：没有自己应用Activity可见，显示悬浮窗")
            }
        } else {
            XLog.w("没有悬浮窗权限")
            stopSelf()
        }

        XLog.d("悬浮窗服务创建完成")
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        try {
            XLog.d("悬浮窗服务开始销毁")

            // 设置销毁标志，防止在销毁过程中执行显示/隐藏操作
            isServiceDestroying = true

            // 移除悬浮窗
            removeFloatingWindow()

            // 注销应用生命周期监听器
            AppLifecycleManager.removeListener(this)

            // 注销用户登录状态监听器
            UserManager.removeLoginStatusListener(this)

            // 停止充值提醒效果
            stopRechargeNotificationEffects()

            // 清理Handler
            rechargeHandler.removeCallbacksAndMessages(null)

            // 取消协程
            countdownObserverJob?.cancel()
            serviceScope.cancel()

            // 停止保活
            if (::keepAliveManager.isInitialized) {
                keepAliveManager.onServiceDestroyed()
            }

            // 清理UI组件引用和动画
            layoutWarning?.clearAnimation()
            tvCountdown = null
            btnHome = null
            layoutWarning = null

            XLog.d("悬浮窗服务销毁完成")

        } catch (e: Exception) {
            XLog.e("悬浮窗服务销毁失败", e)
        } finally {
            super.onDestroy()
        }
    }

    /**
     * 检查是否具有设备所有者权限
     */
    private fun isDeviceOwner(): Boolean {
        return try {
            val devicePolicyManager =
                getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            devicePolicyManager.isDeviceOwnerApp(packageName)
        } catch (e: Exception) {
            XLog.e("检查设备所有者权限失败", e)
            false
        }
    }

    /**
     * 检查悬浮窗权限
     * 如果有设备所有者权限，直接返回true，跳过系统权限检查
     */
    private fun checkOverlayPermission(): Boolean {
        // 没有设备所有者权限，检查系统悬浮窗权限
        return Settings.canDrawOverlays(this)
    }

    /**
     * 创建悬浮窗
     */
    private fun createFloatingWindow() {
        try {
            windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager

            // 创建悬浮窗布局
            floatingView = LayoutInflater.from(this).inflate(R.layout.layout_floating_window, null)

            // 初始化UI组件
            initViews()

            // 设置布局参数
            setupLayoutParams()

            // 设置触摸监听
            setupTouchListener()

            // 添加到窗口管理器
            windowManager?.addView(floatingView, layoutParams)
            isFloatingWindowVisible = true

            XLog.d("悬浮窗创建成功")

        } catch (e: Exception) {
            XLog.e("创建悬浮窗失败", e)
            stopSelf()
        }
    }

    /**
     * 初始化UI组件
     */
    private fun initViews() {
        floatingView?.let { view ->
            tvCountdown = view.findViewById(R.id.tv_countdown)
            btnHome = view.findViewById(R.id.btn_home)
            btnArrow = view.findViewById(R.id.btn_arrow)
            layoutMain = view.findViewById(R.id.layout_main)
            layoutWarning = view.findViewById(R.id.layout_warning)

            // 设置点击监听
            btnHome?.setOnClickListener {
                showConfirmDialog(getString(R.string.confirm_home_message)) {
                    openHomeApp()
                }
            }

            // 箭头按钮点击切换展开/收起
            btnArrow?.setOnClickListener {
                if (isExpanded) {
                    collapseFloatingWindow()
                } else {
                    expandFloatingWindow()
                }
            }

            // 设置警告点击监听
            layoutWarning?.setOnClickListener {
                // 点击警告时也启动密码对话框，进入设置
                showPasswordDialog()
            }

            // 初始化警告显示状态
            updateWarningVisibility()

            // 调试：强制显示警告测试
            XLog.d("调试：layoutWarning是否为null: ${layoutWarning == null}")
            XLog.d("调试：当前管控状态: ${AdminManager.isGovernanceState}")
            XLog.d("调试：悬浮窗是否可见: $isFloatingWindowVisible")

            // 延迟一点再次尝试更新警告，确保UI完全初始化
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                XLog.d("延迟更新警告显示状态")
                updateWarningVisibility()

                // 设置初始状态为收起状态
                initializeCollapsedState()
            }, 500)
        }
    }

    /**
     * 初始化为收起状态
     */
    private fun initializeCollapsedState() {
        layoutMain?.let { layout ->
            // 等待布局完成后再设置初始位置
            layout.post {
                val layoutWidth = layout.width
                val arrowWidth = btnArrow?.width ?: 24
                val targetX = -(layoutWidth - arrowWidth - 16).toFloat()

                // 直接设置位置，不使用动画
                layout.translationX = targetX

                // 设置箭头初始角度
                btnArrow?.rotation = 0f

                isExpanded = false
                XLog.d("悬浮窗初始化为收起状态")
            }
        }
    }


    /**
     * 设置布局参数
     */
    private fun setupLayoutParams() {
        val type =
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY

        layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START
            x = 0  // 贴左边
            y = 200  // 距离顶部200px
        }
    }

    /**
     * 设置触摸监听（支持拖拽）
     * 限制只能在左边上下拖动
     */
    private fun setupTouchListener() {
        floatingView?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = layoutParams?.x ?: 0
                    initialY = layoutParams?.y ?: 0
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    true
                }

                MotionEvent.ACTION_MOVE -> {
                    layoutParams?.let { params ->
                        // 只允许Y轴移动，X轴固定在左边
                        params.x = 0  // 始终贴左边
                        val newY = initialY + (event.rawY - initialTouchY).toInt()

                        // 限制Y轴范围，不能超出屏幕
                        val displayMetrics = resources.displayMetrics
                        val screenHeight = displayMetrics.heightPixels
                        val viewHeight = floatingView?.height ?: 0

                        params.y = when {
                            newY < 0 -> 0
                            newY > screenHeight - viewHeight -> screenHeight - viewHeight
                            else -> newY
                        }

                        windowManager?.updateViewLayout(floatingView, params)
                    }
                    true
                }

                else -> false
            }
        }
    }

    /**
     * 监听倒计时状态变化
     */
    private fun observeCountdownState() {
        countdownObserverJob = serviceScope.launch {
            CountdownManager.countdownData.collect { data ->
                updateCountdownDisplay(data)
            }
        }
    }

    /**
     * 更新倒计时显示
     */
    private fun updateCountdownDisplay(data: CountdownManager.CountdownData) {
        val timeText = if (CountdownManager.isCountdownRunning()) {
            val minutes = TimeUnit.MILLISECONDS.toMinutes(data.remainingTimeMillis)
            val seconds = TimeUnit.MILLISECONDS.toSeconds(data.remainingTimeMillis) % 60
            String.format("%02d:%02d", minutes, seconds)
        } else {
            "00:00"
        }

        // 正常显示倒计时文本
        tvCountdown?.text = timeText

        // 检查是否需要启动动画效果（倒数5分钟开始，且用户已登录）
        val shouldShowAnimation =
            CountdownManager.isCountdownRunning() &&
                    data.remainingTimeMillis <= 5 * 60 * 1000L && // 5分钟 = 300000毫秒
                    UserManager.isUserLoggedIn() // 用户必须已登录

        if (shouldShowAnimation) {
            // 启动动画效果（保留原有的计时文本动画）
            startRechargeNotificationEffects(data.remainingTimeMillis)
        } else {
            // 停止动画效果
            stopRechargeNotificationEffects()

            // 根据倒计时状态改变颜色（正常状态）
            val textColor = if (CountdownManager.isCountdownRunning()) {
                getColor(android.R.color.white)
            } else {
                getColor(android.R.color.holo_red_light)
            }
            tvCountdown?.setTextColor(textColor)
        }

        // 检查是否需要自动展开悬浮窗（5分钟和1分钟时）
        if (CountdownManager.isCountdownRunning() && !isExpanded) {
            val remainingMinutes = TimeUnit.MILLISECONDS.toMinutes(data.remainingTimeMillis)
            if (remainingMinutes == 5L || remainingMinutes == 1L) {
                expandFloatingWindow()
            }
        }
    }

    /**
     * 显示主页确认对话框
     */
    private fun showConfirmDialog(msg: String, run: Runnable) {
        try {
            val alertDialog = AlertDialog.Builder(this, android.R.style.Theme_Material_Dialog_Alert)
                .setTitle(getString(R.string.confirm_home_title))
                .setMessage(msg)
                .setPositiveButton(getString(R.string.confirm)) { _, _ ->
                    run.run()
                }
                .setNegativeButton(getString(R.string.cancel)) { dialog, _ ->
                    dialog.dismiss()
                }
                .setCancelable(true)
                .create()

            // 设置对话框为系统级别，可以在悬浮窗上显示
            alertDialog.window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)

            alertDialog.show()

            XLog.d("显示主页确认对话框")
        } catch (e: Exception) {
            XLog.e("显示主页确认对话框失败，直接执行主页操作", e)
            // 如果对话框显示失败，直接执行原有操作
            openHomeApp()
        }
    }

    /**
     * 打开主页应用
     */
    private fun openHomeApp() {
        WhitelistManager.openHomeApp()
    }

    /**
     * 显示密码对话框
     * 使用原生 Activity 模式，避免 DialogX 在透明 Activity 中的软键盘问题
     */
    private fun showPasswordDialog() {
        try {
            // 使用 AdminManager 的 Activity 模式
            AdminManager.checkWithActivity(this)
            XLog.d("启动原生密码对话框Activity")
        } catch (e: Exception) {
            XLog.e("启动密码对话框Activity失败", e)
        }
    }


    /**
     * 用户登录状态变化回调
     * @param isLoggedIn 是否已登录
     */
    override fun onLoginStatusChanged(isLoggedIn: Boolean) {
        try {
            XLog.d("用户登录状态变化: $isLoggedIn")
        } catch (e: Exception) {
            XLog.e("处理用户登录状态变化失败", e)
        }
    }

    /**
     * 移除悬浮窗
     */
    private fun removeFloatingWindow() {
        try {
            floatingView?.let { view ->
                windowManager?.removeView(view)
                floatingView = null
            }
            isFloatingWindowVisible = false
            XLog.d("悬浮窗已移除")
        } catch (e: Exception) {
            XLog.e("移除悬浮窗失败", e)
        }
    }

    /**
     * 显示悬浮窗
     */
    private fun showFloatingWindow() {
        try {
            // 检查前置条件
            if (isServiceDestroying) {
                XLog.d("服务正在销毁，跳过显示悬浮窗操作")
                return
            }

            if (floatingView == null) {
                XLog.w("显示悬浮窗失败：悬浮窗视图未创建")
                return
            }

            if (windowManager == null) {
                XLog.w("显示悬浮窗失败：WindowManager未初始化")
                return
            }

            if (isFloatingWindowVisible) {
                XLog.d("悬浮窗已经是显示状态，跳过显示操作")
                return
            }

            floatingView?.visibility = View.VISIBLE
            isFloatingWindowVisible = true

            // 更新警告显示状态
            updateWarningVisibility()

            XLog.d("悬浮窗已显示")
            XLog.d("调试：showFloatingWindow中 - layoutWarning是否为null: ${layoutWarning == null}")
            XLog.d("调试：showFloatingWindow中 - 当前管控状态: ${AdminManager.isGovernanceState}")

        } catch (e: Exception) {
            XLog.e("显示悬浮窗失败", e)
        }
    }

    /**
     * 隐藏悬浮窗
     */
    private fun hideFloatingWindow() {
        try {
            // 检查前置条件
            if (isServiceDestroying) {
                XLog.d("服务正在销毁，跳过隐藏悬浮窗操作")
                return
            }

            if (floatingView == null) {
                XLog.w("隐藏悬浮窗失败：悬浮窗视图未创建")
                return
            }

            if (!isFloatingWindowVisible) {
                XLog.d("悬浮窗已经是隐藏状态，跳过隐藏操作")
                return
            }

            floatingView?.visibility = View.GONE
            isFloatingWindowVisible = false
            XLog.d("悬浮窗已隐藏")

        } catch (e: Exception) {
            XLog.e("隐藏悬浮窗失败", e)
        }
    }

    // 实现 AppLifecycleListener 接口
    override fun onForegroundAppChanged(packageName: String, className: String) {
        try {
            XLog.d("收到前台应用变化通知: $packageName($className), 当前悬浮窗状态: ${if (isFloatingWindowVisible) "显示" else "隐藏"}")

            // 检查服务是否正在销毁
            if (isServiceDestroying) {
                XLog.d("服务正在销毁，忽略前台应用变化事件")
                return
            }

            // 检查悬浮窗是否已创建
            if (floatingView == null) {
                XLog.w("悬浮窗尚未创建，忽略前台应用变化事件")
                return
            }

            // 新逻辑：基于具体Activity类名来控制悬浮窗
            val isOwnApp = packageName == this.packageName
            updateFloatingWindowVisibility(isOwnApp, className, packageName)

        } catch (e: Exception) {
            XLog.e("处理前台应用变化失败: $packageName", e)
        }
    }

    // 实现 OwnAppVisibilityListener 接口
    override fun onOwnAppVisibilityChanged(isVisible: Boolean, activityClassName: String) {
        try {
            XLog.d("收到自己应用可见性变化通知: isVisible=$isVisible, activity=$activityClassName")

            // 检查服务是否正在销毁
            if (isServiceDestroying) {
                XLog.d("服务正在销毁，忽略自己应用可见性变化事件")
                return
            }

            // 检查悬浮窗是否已创建
            if (floatingView == null) {
                XLog.w("悬浮窗尚未创建，忽略自己应用可见性变化事件")
                return
            }

            if (isVisible) {
                // 自己应用的Activity变为可见，根据Activity类名决定是否隐藏悬浮窗
                updateFloatingWindowVisibility(true, activityClassName)
            } else {
                // 自己应用的Activity变为不可见，显示悬浮窗
                if (!isFloatingWindowVisible) {
                    showFloatingWindow()
                    XLog.i("自己应用Activity($activityClassName)变为不可见，显示悬浮窗")
                } else {
                    XLog.d("自己应用Activity($activityClassName)变为不可见，悬浮窗已经是显示状态")
                }
            }

        } catch (e: Exception) {
            XLog.e("处理自己应用可见性变化失败: $activityClassName", e)
        }
    }

    /**
     * 更新悬浮窗可见性
     * 基于具体的Activity类名来决定是否显示悬浮窗
     */
    private fun updateFloatingWindowVisibility(
        isOwnAppVisible: Boolean,
        activityClassName: String
    ) {
        updateFloatingWindowVisibility(isOwnAppVisible, activityClassName, null)
    }

    /**
     * 更新悬浮窗可见性
     * 基于具体的Activity类名来决定是否显示悬浮窗
     * @param isOwnAppVisible 是否是自己应用的Activity
     * @param activityClassName Activity的完整类名
     * @param packageName 应用包名，用于按钮显示控制
     */
    private fun updateFloatingWindowVisibility(
        isOwnAppVisible: Boolean,
        activityClassName: String,
        packageName: String?
    ) {
        try {
            val shouldHideFloatingWindow = shouldHideFloatingWindowForActivity(activityClassName)

            if (shouldHideFloatingWindow) {
                // 需要隐藏悬浮窗的Activity
                if (isFloatingWindowVisible) {
                    hideFloatingWindow()
                    XLog.i("Activity($activityClassName)需要隐藏悬浮窗，悬浮窗已隐藏")
                } else {
                    XLog.d("Activity($activityClassName)需要隐藏悬浮窗，悬浮窗已经是隐藏状态")
                }
            } else {
                // 需要显示悬浮窗的Activity或其他应用
                if (!isFloatingWindowVisible) {
                    showFloatingWindow()
                    XLog.i("Activity($activityClassName)需要显示悬浮窗，悬浮窗已显示")
                } else {
                    XLog.d("Activity($activityClassName)需要显示悬浮窗，悬浮窗已经是显示状态")
                    // 即使悬浮窗已经显示，也要更新警告状态
                    updateWarningVisibility()
                }
            }

        } catch (e: Exception) {
            XLog.e("更新悬浮窗可见性失败", e)
        }
    }

    /**
     * 判断指定Activity是否需要隐藏悬浮窗
     * @param activityClassName Activity的完整类名
     * @return true表示需要隐藏悬浮窗，false表示需要显示悬浮窗
     */
    private fun shouldHideFloatingWindowForActivity(activityClassName: String): Boolean {
        return try {

            // 检查是否是例外Activity（即使是自己应用也显示悬浮窗）
            if (exceptionActivityClasses.contains(activityClassName)) {
                XLog.d("Activity($activityClassName)在例外列表中，显示悬浮窗")
                return false
            }

            // 检查是否是自己应用的Activity（通过包名判断）
            if (activityClassName.startsWith(packageName)) {
                XLog.d("Activity($activityClassName)是自己应用的Activity，默认隐藏悬浮窗")
                return true
            }

            // 其他应用的Activity，显示悬浮窗
            XLog.d("Activity($activityClassName)是其他应用，显示悬浮窗")
            false

        } catch (e: Exception) {
            XLog.e("判断Activity是否需要隐藏悬浮窗失败，默认显示", e)
            false
        }
    }


    /**
     * 更新警告显示状态
     * 根据管控模式状态控制警告的显示/隐藏
     */
    private fun updateWarningVisibility() {
        try {
            XLog.d("开始更新警告显示状态")

            if (layoutWarning == null) {
                XLog.w("layoutWarning为null，无法更新警告显示状态")
                return
            }

            layoutWarning?.let { warning ->
                val isGovernanceState = AdminManager.isGovernanceState
                val shouldShowWarning = !isGovernanceState

                XLog.d("警告显示状态检查 - 管控模式: $isGovernanceState, 应该显示警告: $shouldShowWarning")

                if (shouldShowWarning) {
                    warning.visibility = View.VISIBLE
                    // 添加轻微的脉动效果来吸引注意
                    startWarningPulseAnimation(warning)
                    XLog.i("警告已显示并启动动画")
                } else {
                    warning.visibility = View.GONE
                    // 停止动画
                    warning.clearAnimation()
                    XLog.i("警告已隐藏并停止动画")
                }
            }
        } catch (e: Exception) {
            XLog.e("更新警告显示状态失败", e)
        }
    }

    /**
     * 启动警告脉动动画
     */
    private fun startWarningPulseAnimation(view: View) {
        try {
            // 创建组合动画：透明度变化 + 轻微缩放
            val alphaAnimator = ObjectAnimator.ofFloat(view, "alpha", 0.8f, 1.0f)
            alphaAnimator.duration = 2000
            alphaAnimator.repeatCount = ObjectAnimator.INFINITE
            alphaAnimator.repeatMode = ObjectAnimator.REVERSE

            val scaleXAnimator =
                ObjectAnimator.ofFloat(view, "scaleX", 0.98f, 1.02f)
            scaleXAnimator.duration = 2000
            scaleXAnimator.repeatCount = ObjectAnimator.INFINITE
            scaleXAnimator.repeatMode = ObjectAnimator.REVERSE

            val scaleYAnimator =
                ObjectAnimator.ofFloat(view, "scaleY", 0.98f, 1.02f)
            scaleYAnimator.duration = 2000
            scaleYAnimator.repeatCount = ObjectAnimator.INFINITE
            scaleYAnimator.repeatMode = ObjectAnimator.REVERSE

            val animatorSet = AnimatorSet()
            animatorSet.playTogether(alphaAnimator, scaleXAnimator, scaleYAnimator)
            animatorSet.start()
        } catch (e: Exception) {
            XLog.e("启动警告脉动动画失败", e)
        }
    }


    // 实现KeepAliveCapable接口
    override fun getKeepAliveManager(): MutualKeepAliveManager {
        return keepAliveManager
    }

    override fun onPartnerServiceDied(serviceClass: Class<out Service>) {
        super.onPartnerServiceDied(serviceClass)
        XLog.w("悬浮窗服务检测到伙伴服务死亡: ${serviceClass.simpleName}")
    }

    override fun onPartnerServiceRestarted(serviceClass: Class<out Service>) {
        super.onPartnerServiceRestarted(serviceClass)
        XLog.i("悬浮窗服务检测到伙伴服务重启: ${serviceClass.simpleName}")
    }

    /**
     * 启动充值提醒效果
     * @param remainingTimeMillis 剩余时间（毫秒）
     */
    private fun startRechargeNotificationEffects(remainingTimeMillis: Long) {
        if (isRechargeNotificationActive) {
            // 如果已经在运行，先停止当前效果
            stopRechargeNotificationEffects()
        }

        isRechargeNotificationActive = true

        // 启动颜色渐变动画
        startColorGradientAnimation()

        // 启动脉动动画
        startPulseAnimation(remainingTimeMillis)
    }

    /**
     * 停止充值提醒效果
     */
    private fun stopRechargeNotificationEffects() {
        if (!isRechargeNotificationActive) return

        isRechargeNotificationActive = false

        // 停止脉动动画
        pulseAnimator?.cancel()
        pulseAnimator = null

        // 停止颜色动画
        colorAnimator?.cancel()
        colorAnimator = null

        // 重置文字颜色为白色
        tvCountdown?.setTextColor(getColor(android.R.color.white))

        // 重置缩放
        tvCountdown?.scaleX = 1.0f
        tvCountdown?.scaleY = 1.0f
    }

    /**
     * 启动颜色渐变动画（白色到橙色）
     */
    private fun startColorGradientAnimation() {
        val whiteColor = Color.WHITE
        val orangeColor = Color.parseColor("#FF8C00") // 深橙色

        colorAnimator = ValueAnimator.ofArgb(whiteColor, orangeColor).apply {
            duration = 2000 // 2秒渐变
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.REVERSE

            addUpdateListener { animator ->
                val color = animator.animatedValue as Int
                tvCountdown?.setTextColor(color)
            }

            start()
        }
    }

    /**
     * 启动脉动动画
     * @param remainingTimeMillis 剩余时间，用于计算脉动频率
     */
    private fun startPulseAnimation(remainingTimeMillis: Long) {
        // 停止之前的脉动动画
        pulseAnimator?.cancel()

        // 根据剩余时间计算脉动间隔
        val pulseInterval = calculatePulseInterval(remainingTimeMillis)

        // 创建脉动动画集合
        val scaleXAnimator =
            ObjectAnimator.ofFloat(tvCountdown, "scaleX", 1.0f, 1.15f, 1.0f).apply {
                duration = 300 // 单次脉动持续300毫秒
            }

        val scaleYAnimator =
            ObjectAnimator.ofFloat(tvCountdown, "scaleY", 1.0f, 1.15f, 1.0f).apply {
                duration = 300
            }

        // 使用AnimatorSet同步X和Y轴的脉动
        val animatorSet = AnimatorSet().apply {
            playTogether(scaleXAnimator, scaleYAnimator)
        }

        // 启动脉动循环
        startPulseLoop(animatorSet, pulseInterval)
    }

    /**
     * 启动脉动循环
     */
    private fun startPulseLoop(animatorSet: AnimatorSet, interval: Long) {
        if (!isRechargeNotificationActive) return

        animatorSet.start()

        // 安排下一次脉动
        rechargeHandler.postDelayed({
            if (isRechargeNotificationActive) {
                // 重新计算当前剩余时间的脉动频率
                val currentRemainingTime = CountdownManager.getRemainingTime()
                if (currentRemainingTime > 0 && currentRemainingTime <= 5 * 60 * 1000L) {
                    val newInterval = calculatePulseInterval(currentRemainingTime)
                    startPulseLoop(animatorSet, newInterval)
                }
            }
        }, interval)
    }

    /**
     * 根据剩余时间计算脉动间隔
     * @param remainingTimeMillis 剩余时间（毫秒）
     * @return 脉动间隔（毫秒）
     */
    private fun calculatePulseInterval(remainingTimeMillis: Long): Long {
        val remainingMinutes = remainingTimeMillis / (60 * 1000)

        return when {
            remainingMinutes >= 4 -> 3000L  // 4-5分钟：每3秒脉动
            remainingMinutes >= 2 -> 2000L  // 2-4分钟：每2秒脉动
            remainingMinutes >= 1 -> 1000L  // 1-2分钟：每1秒脉动
            else -> 800L                    // <1分钟：每0.8秒脉动
        }
    }

    /**
     * 展开悬浮窗
     */
    private fun expandFloatingWindow() {
        if (isExpanded) return

        isExpanded = true

        layoutMain?.let { layout ->
            // 平移动画：从收起位置移动到展开位置
            val currentX = layout.translationX
            val targetX = 0f

            val translateAnimator =
                ObjectAnimator.ofFloat(layout, "translationX", currentX, targetX)
            translateAnimator.duration = 300

            // 箭头旋转动画：从0度旋转到180度
            btnArrow?.let { arrow ->
                val rotateAnimator = ObjectAnimator.ofFloat(arrow, "rotation", 0f, 180f)
                rotateAnimator.duration = 300

                // 同时执行平移和旋转动画
                val animatorSet = AnimatorSet()
                animatorSet.playTogether(translateAnimator, rotateAnimator)
                animatorSet.start()
            } ?: translateAnimator.start()
        }

        XLog.d("悬浮窗已展开")
    }

    /**
     * 收起悬浮窗
     */
    private fun collapseFloatingWindow() {
        if (!isExpanded) return

        isExpanded = false

        layoutMain?.let { layout ->
            // 计算收起位置：向左移动，只露出箭头部分
            val layoutWidth = layout.width
            val arrowWidth = btnArrow?.width ?: 24
            val targetX = -(layoutWidth - arrowWidth - 16).toFloat() // 16dp为padding

            val translateAnimator = ObjectAnimator.ofFloat(layout, "translationX", 0f, targetX)
            translateAnimator.duration = 300

            // 箭头旋转动画：从180度旋转回0度
            btnArrow?.let { arrow ->
                val rotateAnimator = ObjectAnimator.ofFloat(arrow, "rotation", 180f, 0f)
                rotateAnimator.duration = 300

                // 同时执行平移和旋转动画
                val animatorSet = AnimatorSet()
                animatorSet.playTogether(translateAnimator, rotateAnimator)
                animatorSet.start()
            } ?: translateAnimator.start()
        }

        XLog.d("悬浮窗已收起")
    }
}
